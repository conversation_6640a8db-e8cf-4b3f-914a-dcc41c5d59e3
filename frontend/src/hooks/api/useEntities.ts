import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { entityService } from '@/services/api';
import { CreateEntityRequest, UpdateEntityRequest, EntityType, PersonType, EntityStatus } from '@/types/api';
import { toast } from 'sonner';

// Hook para listar entidades com filtros avançados
export const useEntities = (
  page = 1,
  limit = 10,
  search?: string,
  type?: string,
  entityType?: EntityType,
  personType?: PersonType,
  status?: EntityStatus
) => {
  return useQuery({
    queryKey: ['entities', { page, limit, search, type, entityType, personType, status }],
    queryFn: () => entityService.getEntities(page, limit, search, type, entityType, personType, status),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hooks específicos para clientes e fornecedores
export const useClients = (
  page = 1,
  limit = 10,
  search?: string,
  personType?: PersonType,
  status?: EntityStatus
) => {
  return useQuery({
    queryKey: ['entities', 'clients', { page, limit, search, personType, status }],
    queryFn: () => entityService.getClients(page, limit, search, personType, status),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

export const useSuppliers = (
  page = 1,
  limit = 10,
  search?: string,
  personType?: PersonType,
  status?: EntityStatus
) => {
  return useQuery({
    queryKey: ['entities', 'suppliers', { page, limit, search, personType, status }],
    queryFn: () => entityService.getSuppliers(page, limit, search, personType, status),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para entidades mistas (cliente e fornecedor)
export const useMixed = (
  page = 1,
  limit = 10,
  search?: string,
  personType?: PersonType,
  status?: EntityStatus
) => {
  return useQuery({
    queryKey: ['entities', 'mixed', { page, limit, search, personType, status }],
    queryFn: () => entityService.getMixed(page, limit, search, personType, status),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para entidades por tipo de pessoa
export const useEntitiesByPersonType = (
  personType: PersonType,
  page = 1,
  limit = 10,
  search?: string,
  entityType?: EntityType,
  status?: EntityStatus
) => {
  return useQuery({
    queryKey: ['entities', 'person-type', personType, { page, limit, search, entityType, status }],
    queryFn: () => entityService.getByPersonType(personType, page, limit, search, entityType, status),
    placeholderData: (previousData) => previousData,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para estatísticas das entidades
export const useEntityStatistics = () => {
  return useQuery({
    queryKey: ['entities', 'statistics'],
    queryFn: () => entityService.getStatistics(),
    staleTime: 10 * 60 * 1000, // 10 minutos
  });
};

// Hook para buscar uma entidade específica por ID
export const useEntity = (id: string) => {
  return useQuery({
    queryKey: ['entities', id],
    queryFn: () => entityService.getEntityById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
};

// Hook para criar uma nova entidade
export const useCreateEntity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEntityRequest) => entityService.createEntity(data),
    onSuccess: (newEntity) => {
      queryClient.invalidateQueries({ queryKey: ['entities'] });

      // Invalidar coleções específicas baseadas no novo entityType
      const entityType = newEntity?.entityType || newEntity?.type;
      if (entityType === 'customer') {
        queryClient.invalidateQueries({ queryKey: ['entities', 'clients'] });
      } else if (entityType === 'supplier') {
        queryClient.invalidateQueries({ queryKey: ['entities', 'suppliers'] });
      } else if (entityType === 'both') {
        queryClient.invalidateQueries({ queryKey: ['entities', 'clients'] });
        queryClient.invalidateQueries({ queryKey: ['entities', 'suppliers'] });
        queryClient.invalidateQueries({ queryKey: ['entities', 'mixed'] });
      }

      // Invalidar estatísticas
      queryClient.invalidateQueries({ queryKey: ['entities', 'statistics'] });

      toast.success('Entidade criada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao criar entidade. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para atualizar uma entidade existente
export const useUpdateEntity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateEntityRequest }) =>
      entityService.updateEntity(id, data),
    onSuccess: (updatedEntity) => {
      queryClient.invalidateQueries({ queryKey: ['entities'] });

      // Verificar se a entidade foi retornada corretamente
      if (updatedEntity && updatedEntity.id) {
        queryClient.setQueryData(['entities', updatedEntity.id], updatedEntity);

        // Invalidar todas as coleções específicas pois o tipo pode ter mudado
        queryClient.invalidateQueries({ queryKey: ['entities', 'clients'] });
        queryClient.invalidateQueries({ queryKey: ['entities', 'suppliers'] });
        queryClient.invalidateQueries({ queryKey: ['entities', 'mixed'] });
        queryClient.invalidateQueries({ queryKey: ['entities', 'person-type'] });
      } else {
        console.warn('Entidade atualizada mas resposta inesperada:', updatedEntity);
        // Invalidar todas as queries para garantir consistência
        queryClient.invalidateQueries({ queryKey: ['entities', 'clients'] });
        queryClient.invalidateQueries({ queryKey: ['entities', 'suppliers'] });
        queryClient.invalidateQueries({ queryKey: ['entities', 'mixed'] });
        queryClient.invalidateQueries({ queryKey: ['entities', 'person-type'] });
      }

      // Invalidar estatísticas
      queryClient.invalidateQueries({ queryKey: ['entities', 'statistics'] });

      toast.success('Entidade atualizada com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || 
        'Falha ao atualizar entidade. Por favor, tente novamente.'
      );
    }
  });
};

// Hook para excluir uma entidade
export const useDeleteEntity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => entityService.deleteEntity(id),
    onSuccess: (_data, id) => {
      queryClient.invalidateQueries({ queryKey: ['entities'] });
      queryClient.removeQueries({ queryKey: ['entities', id] });

      // Invalidar todas as coleções específicas
      queryClient.invalidateQueries({ queryKey: ['entities', 'clients'] });
      queryClient.invalidateQueries({ queryKey: ['entities', 'suppliers'] });
      queryClient.invalidateQueries({ queryKey: ['entities', 'mixed'] });
      queryClient.invalidateQueries({ queryKey: ['entities', 'person-type'] });

      // Invalidar estatísticas
      queryClient.invalidateQueries({ queryKey: ['entities', 'statistics'] });

      toast.success('Entidade excluída com sucesso!');
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message ||
        'Falha ao excluir entidade. Por favor, tente novamente.'
      );
    }
  });
};

// Hooks utilitários para transformar entidades em formato de opção para selects

// Hook para opções de fornecedores
export const useSupplierOptions = (
  search?: string,
  personType?: PersonType,
  status?: EntityStatus
) => {
  const { data: suppliersResponse, ...rest } = useSuppliers(1, 100, search, personType, status);

  const options = suppliersResponse?.data?.map(supplier => ({
    id: supplier.id,
    name: supplier.name,
    label: supplier.name,
    value: supplier.id,
    document: supplier.cnpj || supplier.cpf || '',
    entityType: supplier.entityType,
    personType: supplier.personType
  })) || [];

  return {
    data: options,
    suppliers: suppliersResponse?.data || [],
    total: suppliersResponse?.total || 0,
    ...rest
  };
};

// Hook para opções de clientes
export const useClientOptions = (
  search?: string,
  personType?: PersonType,
  status?: EntityStatus
) => {
  const { data: clientsResponse, ...rest } = useClients(1, 100, search, personType, status);

  const options = clientsResponse?.data?.map(client => ({
    id: client.id,
    name: client.name,
    label: client.name,
    value: client.id,
    document: client.cnpj || client.cpf || '',
    entityType: client.entityType,
    personType: client.personType
  })) || [];

  return {
    data: options,
    clients: clientsResponse?.data || [],
    total: clientsResponse?.total || 0,
    ...rest
  };
};

// Hook para opções de entidades (todos os tipos)
export const useEntityOptions = (
  entityType?: EntityType,
  search?: string,
  personType?: PersonType,
  status?: EntityStatus
) => {
  const { data: entitiesResponse, ...rest } = useEntities(1, 100, search, undefined, entityType, personType, status);

  const options = entitiesResponse?.data?.map(entity => ({
    id: entity.id,
    name: entity.name,
    label: entity.name,
    value: entity.id,
    document: entity.cnpj || entity.cpf || '',
    entityType: entity.entityType,
    personType: entity.personType
  })) || [];

  return {
    data: options,
    entities: entitiesResponse?.data || [],
    total: entitiesResponse?.total || 0,
    ...rest
  };
};
