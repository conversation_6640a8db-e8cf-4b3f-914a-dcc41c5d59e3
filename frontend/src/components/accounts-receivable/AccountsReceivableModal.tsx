
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, FileText, RefreshCcw } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import TransactionAmountField from "@/components/transactions/TransactionAmountField";
import { parseCurrencyToNumber } from "@/utils/currencyUtils";
import { MoneyDisplay } from "@/components/ui-custom/MoneyInput";
import { usePaymentMethodOptions, useRecurrenceTypeOptions, useClientOptions } from "@/hooks/api";

const statusOptions = [
  { label: "Pending", value: "pending" },
  { label: "Partial", value: "partial" },
  { label: "Paid", value: "paid" },
];

const categoryOptions = [
  { label: "Income Category 0", value: "Income Category 0" },
  { label: "Income Category 1", value: "Income Category 1" },
  { label: "Income Category 2", value: "Income Category 2" },
  { label: "Income Category 3", value: "Income Category 3" },
  { label: "Income Category 4", value: "Income Category 4" },
];

const projectOptions = [
  { label: "Project 0", value: "Project 0" },
  { label: "Project 2", value: "Project 2" },
  { label: "Project 4", value: "Project 4" },
  { label: "Project 6", value: "Project 6" },
  { label: "Project 8", value: "Project 8" },
];

// Remover array estático - será substituído por dados da API

const bankAccountOptions = [
  { label: "Main Account", value: "main" },
  { label: "Secondary Account", value: "secondary" },
];

const frequencyOptions = [
  { label: "Daily", value: "daily" },
  { label: "Weekly", value: "weekly" },
  { label: "Biweekly", value: "biweekly" },
  { label: "Monthly", value: "monthly" },
  { label: "Quarterly", value: "quarterly" },
  { label: "Semiannually", value: "semiannually" },
  { label: "Annually", value: "annually" },
];

// New options for payment methods

interface AccountsReceivableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialData: any;
}

// Define field names for refreshing
type RefreshableField = 
  | "entity" 
  | "category" 
  | "project" 
  | "bankAccount" 
  | "paymentMethod" 
  | "recurrence";

export default function AccountsReceivableModal({
  open,
  onOpenChange,
  initialData,
}: AccountsReceivableModalProps) {
  const isEditing = !!initialData;
  
  // Buscar dados da API
  const { data: paymentMethodOptions, isLoading: loadingPaymentMethods } = usePaymentMethodOptions();
  const { data: recurrenceOptions, isLoading: loadingRecurrenceTypes } = useRecurrenceTypeOptions();
  const { data: customerOptions, isLoading: loadingCustomers, refetch: refetchCustomers } = useClientOptions();
  
  const [formData, setFormData] = useState({
    description: "",
    entity: "",
    dueDate: new Date(),
    amount: "",
    paidAmount: "",
    status: "pending",
    category: "",
    project: "",
    bankAccount: "",
    notes: "",
    interestAmount: "",
    discountAmount: "",
    markAsPaid: false,
    installments: false,
    numberOfInstallments: "1",
    installmentFrequency: "monthly",
    firstInstallmentDate: new Date(),
    // New fields
    paymentMethod: "",
    recurrence: "none",
    invoiceNumber: "",
  });

  const [openCalendar, setOpenCalendar] = useState(false);
  const [openInstallmentCalendar, setOpenInstallmentCalendar] = useState(false);
  
  // Track which fields are currently being refreshed
  const [refreshingFields, setRefreshingFields] = useState<Record<RefreshableField, boolean>>({
    entity: false,
    category: false,
    project: false,
    bankAccount: false,
    paymentMethod: false,
    recurrence: false
  });
  
  const finalAmount = () => {
    const amount = parseCurrencyToNumber(formData.amount) || 0;
    const interest = parseCurrencyToNumber(formData.interestAmount) || 0;
    const discount = parseCurrencyToNumber(formData.discountAmount) || 0;
    return amount + interest - discount;
  };

  useEffect(() => {
    if (initialData) {
      setFormData({
        description: initialData.description || "",
        entity: initialData.entity || "",
        dueDate: initialData.dueDate || new Date(),
        amount: initialData.amount?.toString() || "",
        paidAmount: initialData.paidAmount?.toString() || "",
        status: initialData.status || "pending",
        category: initialData.category || "",
        project: initialData.project || "",
        bankAccount: initialData.bankAccount || "",
        notes: initialData.notes || "",
        interestAmount: initialData.interestAmount?.toString() || "",
        discountAmount: initialData.discountAmount?.toString() || "",
        markAsPaid: initialData.status === "paid" || false,
        installments: initialData.installments > 1 || false,
        numberOfInstallments: initialData.installments?.toString() || "1",
        installmentFrequency: initialData.recurrence || "monthly",
        firstInstallmentDate: initialData.dueDate || new Date(),
        // Initialize new fields
        paymentMethod: initialData.paymentMethod || "",
        recurrence: initialData.recurrence || "none",
        invoiceNumber: initialData.invoiceNumber || "",
      });
    } else {
      setFormData({
        description: "",
        entity: "",
        dueDate: new Date(),
        amount: "",
        paidAmount: "",
        status: "pending",
        category: "",
        project: "",
        bankAccount: "",
        notes: "",
        interestAmount: "",
        discountAmount: "",
        markAsPaid: false,
        installments: false,
        numberOfInstallments: "1",
        installmentFrequency: "monthly",
        firstInstallmentDate: new Date(),
        // New fields
        paymentMethod: "",
        recurrence: "none",
        invoiceNumber: "",
      });
    }
  }, [initialData, open]);

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
    
    if (field === "markAsPaid") {
      setFormData(prev => ({
        ...prev,
        [field]: value,
        status: value ? "paid" : "pending",
        paidAmount: value ? prev.amount : "0",
      }));
    }
  };

  const handleSubmit = () => {
    toast({
      title: isEditing ? "Conta atualizada" : "Conta cadastrada",
      description: `A conta foi ${isEditing ? "atualizada" : "cadastrada"} com sucesso.`,
    });
    onOpenChange(false);
  };

  // Function to refresh specific field options
  const refreshFieldOptions = (field: RefreshableField) => {
    // Set only the specific field to refreshing state
    setRefreshingFields(prev => ({
      ...prev,
      [field]: true
    }));

    // Executar refetch baseado no campo
    const refreshPromise = (() => {
      switch (field) {
        case "entity":
          return refetchCustomers();
        default:
          return Promise.resolve();
      }
    })();

    refreshPromise.finally(() => {
      setTimeout(() => {
        // Reset only the specific field refreshing state
        setRefreshingFields(prev => ({
          ...prev,
          [field]: false
        }));

        // Show success toast with the specific field name
        const fieldDisplayNames: Record<RefreshableField, string> = {
          entity: "clientes",
          category: "categorias",
          project: "projetos",
          bankAccount: "contas bancárias",
          paymentMethod: "métodos de pagamento",
          recurrence: "tipos de recorrência"
        };

        toast({
          title: "Opções atualizadas",
          description: `A lista de ${fieldDisplayNames[field]} foi atualizada com sucesso.`,
        });
      }, 300);
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Account Receivable" : "Add New Account Receivable"}</DialogTitle>
          <DialogDescription>
            Fill out the details for this account receivable entry.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Customer</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("entity")}
                  disabled={refreshingFields.entity}
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.entity ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select
                value={formData.entity}
                onValueChange={(value) => handleChange("entity", value)}
                disabled={loadingCustomers}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={loadingCustomers ? "Carregando clientes..." : "Selecione o cliente..."} />
                </SelectTrigger>
                <SelectContent>
                  {customerOptions?.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                      {option.document && (
                        <span className="text-xs text-gray-500 ml-2">
                          ({option.document})
                        </span>
                      )}
                    </SelectItem>
                  ))}
                  {!loadingCustomers && (!customerOptions || customerOptions.length === 0) && (
                    <SelectItem value="" disabled>
                      Nenhum cliente encontrado
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Due Date</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <Popover open={openCalendar} onOpenChange={setOpenCalendar}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal mt-1 h-10 flex items-center"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.dueDate ? format(formData.dueDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.dueDate}
                    onSelect={(date) => {
                      handleChange("dueDate", date || new Date());
                      setOpenCalendar(false);
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="amount">Amount</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <TransactionAmountField
                id="amount"
                value={formData.amount}
                onChange={(value) => handleChange("amount", value)}
                isMainAmount={true}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="paidAmount">Received Amount</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <TransactionAmountField
                id="paidAmount"
                value={formData.paidAmount}
                onChange={(value) => handleChange("paidAmount", value)}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Status</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <Select 
                value={formData.status}
                onValueChange={(value) => handleChange("status", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select status..." />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Category</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("category")}
                  disabled={refreshingFields.category}
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.category ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.category}
                onValueChange={(value) => handleChange("category", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select category..." />
                </SelectTrigger>
                <SelectContent>
                  {categoryOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Project (Optional)</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("project")}
                  disabled={refreshingFields.project}
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.project ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.project}
                onValueChange={(value) => handleChange("project", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select project..." />
                </SelectTrigger>
                <SelectContent>
                  {projectOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Bank Account</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("bankAccount")}
                  disabled={refreshingFields.bankAccount}
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.bankAccount ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.bankAccount}
                onValueChange={(value) => handleChange("bankAccount", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select account..." />
                </SelectTrigger>
                <SelectContent>
                  {bankAccountOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Payment Method field */}
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Método de Pagamento</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("paymentMethod")}
                  disabled={refreshingFields.paymentMethod}
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.paymentMethod ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.paymentMethod}
                onValueChange={(value) => handleChange("paymentMethod", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Selecione o método..." />
                </SelectTrigger>
                <SelectContent>
                  {paymentMethodOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Recurrence field */}
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Recorrência</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("recurrence")}
                  disabled={refreshingFields.recurrence}
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.recurrence ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.recurrence}
                onValueChange={(value) => handleChange("recurrence", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Selecione a recorrência..." />
                </SelectTrigger>
                <SelectContent>
                  {frequencyOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Invoice Number field */}
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="invoiceNumber" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Fatura
                </Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <Input
                id="invoiceNumber"
                value={formData.invoiceNumber}
                onChange={(e) => handleChange("invoiceNumber", e.target.value)}
                placeholder="Número da fatura"
                className="mt-1"
              />
            </div>
            
            <div className="col-span-2 flex items-center space-x-2 mt-2">
              <Checkbox 
                id="markAsPaid" 
                checked={formData.markAsPaid}
                onCheckedChange={(checked) => 
                  handleChange("markAsPaid", checked)
                }
              />
              <Label htmlFor="markAsPaid" className="cursor-pointer">
                Mark as paid
              </Label>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="interestAmount">Interest (R$)</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <TransactionAmountField
                id="interestAmount"
                value={formData.interestAmount}
                onChange={(value) => handleChange("interestAmount", value)}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="discountAmount">Discount (R$)</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <TransactionAmountField
                id="discountAmount"
                value={formData.discountAmount}
                onChange={(value) => handleChange("discountAmount", value)}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="finalAmount">Final Amount</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <div className="mt-1 p-2 border rounded-md">
                <MoneyDisplay value={finalAmount()} className="font-semibold" />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Original Amount + Interest - Discount
              </p>
            </div>
            
            <div className="col-span-2 flex items-center space-x-2 mt-2">
              <Checkbox 
                id="installments" 
                checked={formData.installments}
                onCheckedChange={(checked) => 
                  handleChange("installments", !!checked)
                }
              />
              <Label htmlFor="installments" className="cursor-pointer">
                Split into installments
              </Label>
            </div>
            
            {formData.installments && (
              <div className="col-span-2 border rounded-md p-4 mt-2">
                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-1">
                    <div className="flex items-center justify-between h-5">
                      <Label htmlFor="numberOfInstallments">Number of installments</Label>
                      <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
                    </div>
                    <Input
                      id="numberOfInstallments"
                      type="number"
                      min="2"
                      value={formData.numberOfInstallments}
                      onChange={(e) => handleChange("numberOfInstallments", e.target.value)}
                      placeholder="1"
                      className="mt-1"
                    />
                  </div>
                  
                  <div className="col-span-1">
                    <div className="flex items-center justify-between h-5">
                      <Label>Frequency</Label>
                      <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
                    </div>
                    <Select 
                      value={formData.installmentFrequency}
                      onValueChange={(value) => handleChange("installmentFrequency", value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select frequency..." />
                      </SelectTrigger>
                      <SelectContent>
                        {frequencyOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="col-span-2">
                    <div className="flex items-center justify-between h-5">
                      <Label>First installment date</Label>
                      <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
                    </div>
                    <Popover open={openInstallmentCalendar} onOpenChange={setOpenInstallmentCalendar}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal mt-1 h-10 flex items-center"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.firstInstallmentDate ? format(formData.firstInstallmentDate, 'PPP') : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.firstInstallmentDate}
                          onSelect={(date) => {
                            handleChange("firstInstallmentDate", date || new Date());
                            setOpenInstallmentCalendar(false);
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
            )}
            
            <div className="col-span-2">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="notes">Notes</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <Input
                id="notes"
                value={formData.notes}
                onChange={(e) => handleChange("notes", e.target.value)}
                className="mt-1"
              />
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            {isEditing ? "Save Changes" : "Create"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
