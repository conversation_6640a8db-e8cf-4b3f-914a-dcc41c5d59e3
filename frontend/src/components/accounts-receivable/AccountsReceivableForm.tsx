import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, FileText, RefreshCcw } from "lucide-react";
import { format } from "date-fns";
import { RefreshableField, statusOptions, recurrenceOptions, paymentMethodOptions } from './constants';
import { useClientOptions } from '@/hooks/api';
import TransactionAmountField from '@/components/transactions/TransactionAmountField';

// TODO: Implementar hooks para buscar dados via API
// const { data: categories } = useCategories();
// const { data: projects } = useProjects();
// const { data: bankAccounts } = useBankAccounts();

// Temporariamente usar arrays vazios até implementar hooks
const categoryOptions: { label: string; value: string }[] = [];
const projectOptions: { label: string; value: string }[] = [];
const bankAccountOptions: { label: string; value: string }[] = [];

interface FormData {
  description: string;
  entity: string;
  dueDate: Date;
  amount: string;
  paidAmount: string;
  status: string;
  category: string;
  project: string;
  bankAccount: string;
  notes: string;
  interestAmount: string;
  discountAmount: string;
  markAsPaid: boolean;
  installments: boolean;
  numberOfInstallments: string;
  installmentFrequency: string;
  firstInstallmentDate: Date;
  recurrence: string;
  paymentMethod: string;
  invoiceNumber: string;
}

interface AccountsReceivableFormProps {
  isReadOnly: boolean;
  formData: FormData;
  refreshingFields: Record<RefreshableField, boolean>;
  openCalendar: boolean;
  setOpenCalendar: (open: boolean) => void;
  openInstallmentCalendar: boolean;
  setOpenInstallmentCalendar: (open: boolean) => void;
  handleChange: (field: string, value: any) => void;
  refreshFieldOptions: (field: RefreshableField) => void;
  finalAmount: () => string;
}

export const AccountsReceivableForm: React.FC<AccountsReceivableFormProps> = ({
  isReadOnly,
  formData,
  refreshingFields,
  openCalendar,
  setOpenCalendar,
  openInstallmentCalendar,
  setOpenInstallmentCalendar,
  handleChange,
  refreshFieldOptions,
  finalAmount,
}) => {
  // Buscar dados da API
  const { data: customerOptions, isLoading: loadingCustomers } = useClientOptions();
  return (
    <div className="grid gap-4 py-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="col-span-2">
          <Label htmlFor="description">Descrição</Label>
          <Input
            id="description"
            value={formData.description}
            onChange={(e) => handleChange("description", e.target.value)}
            className="mt-1"
            readOnly={isReadOnly}
          />
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label>Cliente</Label>
            {!isReadOnly && (
              <Button 
                type="button"
                variant="ghost" 
                size="icon" 
                className="h-5 w-5" 
                onClick={() => refreshFieldOptions("entity")}
                disabled={refreshingFields.entity}
              >
                <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.entity ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {isReadOnly && <div className="w-5 h-5"></div>}
          </div>
          {isReadOnly ? (
            <Input
              value={formData.entity}
              className="mt-1"
              readOnly
            />
          ) : (
            <Select
              value={formData.entity}
              onValueChange={(value) => handleChange("entity", value)}
              disabled={isReadOnly || loadingCustomers}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder={loadingCustomers ? "Carregando clientes..." : "Selecione o cliente..."} />
              </SelectTrigger>
              <SelectContent>
                {customerOptions?.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                    {option.document && (
                      <span className="text-xs text-gray-500 ml-2">
                        ({option.document})
                      </span>
                    )}
                  </SelectItem>
                ))}
                {!loadingCustomers && (!customerOptions || customerOptions.length === 0) && (
                  <SelectItem value="" disabled>
                    Nenhum cliente encontrado
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          )}
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label>Data de Vencimento</Label>
            <div className="w-5 h-5"></div>
          </div>
          {isReadOnly ? (
            <Input
              value={formData.dueDate ? format(formData.dueDate, 'PPP') : ''}
              className="mt-1"
              readOnly
            />
          ) : (
            <Popover open={openCalendar} onOpenChange={setOpenCalendar}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal mt-1"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.dueDate ? format(formData.dueDate, 'PPP') : <span>Selecione uma data</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData.dueDate}
                  onSelect={(date) => {
                    handleChange("dueDate", date || new Date());
                    setOpenCalendar(false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          )}
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label htmlFor="amount">Valor</Label>
            <div className="w-5 h-5"></div>
          </div>
          <TransactionAmountField
            id="amount"
            label=""
            value={formData.amount}
            onChange={(value) => handleChange("amount", value)}
            disabled={isReadOnly}
            isMainAmount={true}
          />
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label htmlFor="paidAmount">Valor Recebido</Label>
            <div className="w-5 h-5"></div>
          </div>
          <TransactionAmountField
            id="paidAmount"
            label=""
            value={formData.paidAmount}
            onChange={(value) => handleChange("paidAmount", value)}
            disabled={isReadOnly || !formData.markAsPaid}
          />
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label>Status</Label>
            <div className="w-5 h-5"></div>
          </div>
          {isReadOnly ? (
            <Input
              value={statusOptions.find(s => s.value === formData.status)?.label || formData.status}
              className="mt-1"
              readOnly
            />
          ) : (
            <Select 
              value={formData.status}
              onValueChange={(value) => handleChange("status", value)}
              disabled={isReadOnly}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Selecione o status..." />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label>Categoria</Label>
            {!isReadOnly && (
              <Button 
                type="button"
                variant="ghost" 
                size="icon" 
                className="h-5 w-5" 
                onClick={() => refreshFieldOptions("category")}
                disabled={refreshingFields.category}
              >
                <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.category ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {isReadOnly && <div className="w-5 h-5"></div>}
          </div>
          {isReadOnly ? (
            <Input
              value={formData.category}
              className="mt-1"
              readOnly
            />
          ) : (
            <Select 
              value={formData.category}
              onValueChange={(value) => handleChange("category", value)}
              disabled={isReadOnly}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Selecione a categoria..." />
              </SelectTrigger>
              <SelectContent>
                {categoryOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label>Método de Pagamento</Label>
            {!isReadOnly && (
              <Button 
                type="button"
                variant="ghost" 
                size="icon" 
                className="h-5 w-5" 
                onClick={() => refreshFieldOptions("paymentMethod")}
                disabled={refreshingFields.paymentMethod}
                title="Atualizar opções"
              >
                <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.paymentMethod ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {isReadOnly && <div className="w-5 h-5"></div>}
          </div>
          {isReadOnly ? (
            <Input
              value={paymentMethodOptions.find(p => p.value === formData.paymentMethod)?.label || formData.paymentMethod}
              className="mt-1"
              readOnly
            />
          ) : (
            <Select 
              value={formData.paymentMethod}
              onValueChange={(value) => handleChange("paymentMethod", value)}
              disabled={isReadOnly}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Selecione o método..." />
              </SelectTrigger>
              <SelectContent>
                {paymentMethodOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label htmlFor="invoiceNumber" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Fatura
            </Label>
            <div className="w-5 h-5"></div>
          </div>
          <Input
            id="invoiceNumber"
            value={formData.invoiceNumber}
            onChange={(e) => handleChange("invoiceNumber", e.target.value)}
            placeholder="Número da fatura"
            className="mt-1"
            readOnly={isReadOnly}
          />
        </div>

        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label>Projeto (Opcional)</Label>
            {!isReadOnly && (
              <Button 
                type="button"
                variant="ghost" 
                size="icon" 
                className="h-5 w-5" 
                onClick={() => refreshFieldOptions("project")}
                disabled={refreshingFields.project}
                title="Atualizar opções"
              >
                <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.project ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {isReadOnly && <div className="w-5 h-5"></div>}
          </div>
          {isReadOnly ? (
            <Input
              value={formData.project}
              className="mt-1"
              readOnly
            />
          ) : (
            <Select 
              value={formData.project}
              onValueChange={(value) => handleChange("project", value)}
              disabled={isReadOnly}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Selecione o projeto..." />
              </SelectTrigger>
              <SelectContent>
                {projectOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label>Conta Bancária</Label>
            {!isReadOnly && (
              <Button 
                type="button"
                variant="ghost" 
                size="icon" 
                className="h-5 w-5" 
                onClick={() => refreshFieldOptions("bankAccount")}
                disabled={refreshingFields.bankAccount}
                title="Atualizar opções"
              >
                <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.bankAccount ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {isReadOnly && <div className="w-5 h-5"></div>}
          </div>
          {isReadOnly ? (
            <Input
              value={bankAccountOptions.find(b => b.value === formData.bankAccount)?.label || formData.bankAccount}
              className="mt-1"
              readOnly
            />
          ) : (
            <Select 
              value={formData.bankAccount}
              onValueChange={(value) => handleChange("bankAccount", value)}
              disabled={isReadOnly}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Selecione a conta..." />
              </SelectTrigger>
              <SelectContent>
                {bankAccountOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        
        <div className="col-span-1">
          <div className="flex items-center justify-between h-5">
            <Label>Recorrência</Label>
            {!isReadOnly && (
              <Button 
                type="button"
                variant="ghost" 
                size="icon" 
                className="h-5 w-5" 
                onClick={() => refreshFieldOptions("recurrence")}
                disabled={refreshingFields.recurrence}
                title="Atualizar opções"
              >
                <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.recurrence ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {isReadOnly && <div className="w-5 h-5"></div>}
          </div>
          {isReadOnly ? (
            <Input
              value={recurrenceOptions.find(r => r.value === formData.recurrence)?.label || formData.recurrence}
              className="mt-1"
              readOnly
            />
          ) : (
            <Select 
              value={formData.recurrence}
              onValueChange={(value) => handleChange("recurrence", value)}
              disabled={isReadOnly}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Selecione a recorrência..." />
              </SelectTrigger>
              <SelectContent>
                {recurrenceOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        
        {!isReadOnly && (
          <div className="col-span-2 flex items-center space-x-2 mt-2">
            <Checkbox 
              id="markAsPaid" 
              checked={formData.markAsPaid}
              onCheckedChange={(checked) => 
                handleChange("markAsPaid", checked)
              }
              disabled={isReadOnly}
            />
            <Label htmlFor="markAsPaid" className="cursor-pointer">
              Marcar como recebido
            </Label>
          </div>
        )}
      </div>
    </div>
  );
}; 