import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, FileText, RefreshCcw } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { usePaymentMethodOptions, useRecurrenceTypeOptions, useSupplierOptions } from "@/hooks/api";
import TransactionAmountField from "../transactions/TransactionAmountField";
import { parseCurrencyToNumber, numberToFormattedString } from "@/utils/currencyUtils";

const statusOptions = [
  { label: "Pending", value: "pending" },
  { label: "Partial", value: "partial" },
  { label: "Paid", value: "paid" },
];

const categoryOptions = [
  { label: "Expense Category 0", value: "Expense Category 0" },
  { label: "Expense Category 1", value: "Expense Category 1" },
  { label: "Expense Category 2", value: "Expense Category 2" },
  { label: "Expense Category 3", value: "Expense Category 3" },
  { label: "Expense Category 4", value: "Expense Category 4" },
];

const projectOptions = [
  { label: "Project 0", value: "Project 0" },
  { label: "Project 2", value: "Project 2" },
  { label: "Project 4", value: "Project 4" },
  { label: "Project 6", value: "Project 6" },
  { label: "Project 8", value: "Project 8" },
];

// Remover array estático - será substituído por dados da API

const bankAccountOptions = [
  { label: "Main Account", value: "main" },
  { label: "Secondary Account", value: "secondary" },
];


interface AccountsPayableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialData: any;
}

type RefreshableField = 
  | "entity" 
  | "category" 
  | "project" 
  | "bankAccount" 
  | "paymentMethod" 
  | "recurrence";

export default function AccountsPayableModal({
  open,
  onOpenChange,
  initialData,
}: AccountsPayableModalProps) {
  const isEditing = !!initialData;
  
  // Buscar dados da API
  const { data: paymentMethodOptions, isLoading: loadingPaymentMethods } = usePaymentMethodOptions();
  const { data: recurrenceOptions, isLoading: loadingRecurrenceTypes } = useRecurrenceTypeOptions();
  const { data: supplierOptions, isLoading: loadingSuppliers, refetch: refetchSuppliers } = useSupplierOptions();
  
  const [formData, setFormData] = useState({
    description: "",
    entity: "",
    dueDate: new Date(),
    amount: "0,00",
    paidAmount: "0,00",
    status: "pending",
    category: "",
    project: "",
    bankAccount: "",
    notes: "",
    interestAmount: "0,00",
    discountAmount: "0,00",
    markAsPaid: false,
    installments: false,
    numberOfInstallments: "1",
    installmentFrequency: "monthly",
    firstInstallmentDate: new Date(),
    recurrence: "none",
    paymentMethod: "",
    invoiceNumber: "",
  });

  const [openCalendar, setOpenCalendar] = useState(false);
  const [openInstallmentCalendar, setOpenInstallmentCalendar] = useState(false);
  
  const [refreshingFields, setRefreshingFields] = useState<Record<RefreshableField, boolean>>({
    entity: false,
    category: false,
    project: false,
    bankAccount: false,
    paymentMethod: false,
    recurrence: false
  });
  
  const finalAmount = () => {
    const amount = parseCurrencyToNumber(formData.amount) || 0;
    const interest = parseCurrencyToNumber(formData.interestAmount) || 0;
    const discount = parseCurrencyToNumber(formData.discountAmount) || 0;
    return numberToFormattedString(amount + interest - discount);
  };

  useEffect(() => {
    if (initialData) {
      const amountStr = initialData.amount ? numberToFormattedString(initialData.amount) : "0,00";
      const paidAmountStr = initialData.paidAmount ? numberToFormattedString(initialData.paidAmount) : "0,00";
      const interestStr = initialData.interestAmount ? numberToFormattedString(initialData.interestAmount) : "0,00";
      const discountStr = initialData.discountAmount ? numberToFormattedString(initialData.discountAmount) : "0,00";
      
      setFormData({
        description: initialData.description || "",
        entity: initialData.entity || "",
        dueDate: initialData.dueDate || new Date(),
        amount: amountStr,
        paidAmount: paidAmountStr,
        status: initialData.status || "pending",
        category: initialData.category || "",
        project: initialData.project || "",
        bankAccount: initialData.bankAccount || "",
        notes: initialData.notes || "",
        interestAmount: interestStr,
        discountAmount: discountStr,
        markAsPaid: initialData.status === "paid" || false,
        installments: initialData.installments > 1 || false,
        numberOfInstallments: initialData.installments?.toString() || "1",
        installmentFrequency: initialData.recurrence || "monthly",
        firstInstallmentDate: initialData.dueDate || new Date(),
        recurrence: initialData.recurrence || "none",
        paymentMethod: initialData.paymentMethod || "",
        invoiceNumber: initialData.invoiceNumber || "",
      });
    } else {
      setFormData({
        description: "",
        entity: "",
        dueDate: new Date(),
        amount: "0,00",
        paidAmount: "0,00",
        status: "pending",
        category: "",
        project: "",
        bankAccount: "",
        notes: "",
        interestAmount: "0,00",
        discountAmount: "0,00",
        markAsPaid: false,
        installments: false,
        numberOfInstallments: "1",
        installmentFrequency: "monthly",
        firstInstallmentDate: new Date(),
        recurrence: "none",
        paymentMethod: "",
        invoiceNumber: "",
      });
    }
  }, [initialData, open]);

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
    
    if (field === "markAsPaid") {
      setFormData(prev => ({
        ...prev,
        [field]: value,
        status: value ? "paid" : "pending",
        paidAmount: value ? prev.amount : "0,00",
      }));
    }
  };

  const handleSubmit = () => {
    const submissionData = {
      ...formData,
      amount: parseCurrencyToNumber(formData.amount),
      paidAmount: parseCurrencyToNumber(formData.paidAmount),
      interestAmount: parseCurrencyToNumber(formData.interestAmount),
      discountAmount: parseCurrencyToNumber(formData.discountAmount)
    };
    
    console.log("Submitting form data:", submissionData);
    toast({
      title: isEditing ? "Conta atualizada" : "Conta cadastrada",
      description: `A conta foi ${isEditing ? "atualizada" : "cadastrada"} com sucesso.`,
    });
    onOpenChange(false);
  };

  const refreshFieldOptions = (field: RefreshableField) => {
    setRefreshingFields(prev => ({
      ...prev,
      [field]: true
    }));

    // Executar refetch baseado no campo
    const refreshPromise = (() => {
      switch (field) {
        case "entity":
          return refetchSuppliers();
        default:
          return Promise.resolve();
      }
    })();

    refreshPromise.finally(() => {
      setTimeout(() => {
        setRefreshingFields(prev => ({
          ...prev,
          [field]: false
        }));

        const fieldDisplayNames: Record<RefreshableField, string> = {
          entity: "fornecedores",
          category: "categorias",
          project: "projetos",
          bankAccount: "contas bancárias",
          paymentMethod: "métodos de pagamento",
          recurrence: "tipos de recorrência"
        };

        toast({
          title: "Opções atualizadas",
          description: `A lista de ${fieldDisplayNames[field]} foi atualizada com sucesso.`,
        });
      }, 300);
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Account Payable" : "Add New Account Payable"}</DialogTitle>
          <DialogDescription>
            Fill out the details for this account payable entry.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Supplier</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("entity")}
                  disabled={refreshingFields.entity}
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.entity ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select
                value={formData.entity}
                onValueChange={(value) => handleChange("entity", value)}
                disabled={loadingSuppliers}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder={loadingSuppliers ? "Carregando fornecedores..." : "Selecione o fornecedor..."} />
                </SelectTrigger>
                <SelectContent>
                  {supplierOptions?.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                      {option.document && (
                        <span className="text-xs text-gray-500 ml-2">
                          ({option.document})
                        </span>
                      )}
                    </SelectItem>
                  ))}
                  {!loadingSuppliers && (!supplierOptions || supplierOptions.length === 0) && (
                    <SelectItem value="" disabled>
                      Nenhum fornecedor encontrado
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Due Date</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <Popover open={openCalendar} onOpenChange={setOpenCalendar}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal mt-1"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formData.dueDate ? format(formData.dueDate, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.dueDate}
                    onSelect={(date) => {
                      handleChange("dueDate", date || new Date());
                      setOpenCalendar(false);
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="amount">Amount</Label>
              <TransactionAmountField
                id="amount"
                label=""
                value={formData.amount}
                onChange={(value) => handleChange("amount", value)}
                className="mt-1"
                isMainAmount={true}
              />
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="paidAmount">Paid Amount</Label>
              <TransactionAmountField
                id="paidAmount"
                label=""
                value={formData.paidAmount}
                onChange={(value) => handleChange("paidAmount", value)}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Status</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <Select 
                value={formData.status}
                onValueChange={(value) => handleChange("status", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select status..." />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Category</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("category")}
                  disabled={refreshingFields.category}
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.category ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.category}
                onValueChange={(value) => handleChange("category", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select category..." />
                </SelectTrigger>
                <SelectContent>
                  {categoryOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Método de Pagamento</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("paymentMethod")}
                  disabled={refreshingFields.paymentMethod}
                  title="Atualizar opções"
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.paymentMethod ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.paymentMethod}
                onValueChange={(value) => handleChange("paymentMethod", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Selecione o método..." />
                </SelectTrigger>
                <SelectContent>
                  {paymentMethodOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="invoiceNumber" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Fatura
                </Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <Input
                id="invoiceNumber"
                value={formData.invoiceNumber}
                onChange={(e) => handleChange("invoiceNumber", e.target.value)}
                placeholder="Número da fatura"
                className="mt-1"
              />
            </div>

            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Project (Optional)</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("project")}
                  disabled={refreshingFields.project}
                  title="Atualizar opções"
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.project ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.project}
                onValueChange={(value) => handleChange("project", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select project..." />
                </SelectTrigger>
                <SelectContent>
                  {projectOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Bank Account</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("bankAccount")}
                  disabled={refreshingFields.bankAccount}
                  title="Atualizar opções"
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.bankAccount ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.bankAccount}
                onValueChange={(value) => handleChange("bankAccount", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select account..." />
                </SelectTrigger>
                <SelectContent>
                  {bankAccountOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-1">
              <div className="flex items-center justify-between h-5">
                <Label>Recorrência</Label>
                <Button 
                  type="button"
                  variant="ghost" 
                  size="icon" 
                  className="h-5 w-5" 
                  onClick={() => refreshFieldOptions("recurrence")}
                  disabled={refreshingFields.recurrence}
                  title="Atualizar opções"
                >
                  <RefreshCcw className={`h-3.5 w-3.5 ${refreshingFields.recurrence ? 'animate-spin' : ''}`} />
                </Button>
              </div>
              <Select 
                value={formData.recurrence}
                onValueChange={(value) => handleChange("recurrence", value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Selecione a recorrência..." />
                </SelectTrigger>
                <SelectContent>
                  {recurrenceOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="col-span-2 flex items-center space-x-2 mt-2">
              <Checkbox 
                id="markAsPaid" 
                checked={formData.markAsPaid}
                onCheckedChange={(checked) => 
                  handleChange("markAsPaid", checked)
                }
              />
              <Label htmlFor="markAsPaid" className="cursor-pointer">
                Mark as paid
              </Label>
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="interestAmount">Interest (R$)</Label>
              <TransactionAmountField
                id="interestAmount"
                label=""
                value={formData.interestAmount}
                onChange={(value) => handleChange("interestAmount", value)}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="discountAmount">Discount (R$)</Label>
              <TransactionAmountField
                id="discountAmount"
                label=""
                value={formData.discountAmount}
                onChange={(value) => handleChange("discountAmount", value)}
                className="mt-1"
              />
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="finalAmount">Final Amount</Label>
              <TransactionAmountField
                id="finalAmount"
                label=""
                value={finalAmount()}
                onChange={() => {}} // Read-only
                className="mt-1 font-medium"
                disabled={true}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Original Amount + Interest - Discount
              </p>
            </div>
            
            <div className="col-span-2 flex items-center space-x-2 mt-2">
              <Checkbox 
                id="installments" 
                checked={formData.installments}
                onCheckedChange={(checked) => 
                  handleChange("installments", !!checked)
                }
              />
              <Label htmlFor="installments" className="cursor-pointer">
                Split into installments
              </Label>
            </div>
            
            {formData.installments && (
              <div className="col-span-2 border rounded-md p-4 mt-2">
                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-1">
                    <div className="flex items-center justify-between h-5">
                      <Label htmlFor="numberOfInstallments">Number of installments</Label>
                      <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
                    </div>
                    <Input
                      id="numberOfInstallments"
                      type="number"
                      min="2"
                      value={formData.numberOfInstallments}
                      onChange={(e) => handleChange("numberOfInstallments", e.target.value)}
                      placeholder="1"
                      className="mt-1"
                    />
                  </div>
                  
                  <div className="col-span-1">
                    <div className="flex items-center justify-between h-5">
                      <Label>Frequency</Label>
                      <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
                    </div>
                    <Select 
                      value={formData.installmentFrequency}
                      onValueChange={(value) => handleChange("installmentFrequency", value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select frequency..." />
                      </SelectTrigger>
                      <SelectContent>
                        {recurrenceOptions.filter(option => option.value !== "none").map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="col-span-2">
                    <div className="flex items-center justify-between h-5">
                      <Label>First installment date</Label>
                      <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
                    </div>
                    <Popover open={openInstallmentCalendar} onOpenChange={setOpenInstallmentCalendar}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal mt-1"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.firstInstallmentDate ? format(formData.firstInstallmentDate, 'PPP') : <span>Pick a date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.firstInstallmentDate}
                          onSelect={(date) => {
                            handleChange("firstInstallmentDate", date || new Date());
                            setOpenInstallmentCalendar(false);
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
            )}
            
            <div className="col-span-2">
              <div className="flex items-center justify-between h-5">
                <Label htmlFor="notes">Notes</Label>
                <div className="w-5 h-5"></div> {/* Placeholder for alignment */}
              </div>
              <Input
                id="notes"
                value={formData.notes}
                onChange={(e) => handleChange("notes", e.target.value)}
                className="mt-1"
              />
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            {isEditing ? "Save Changes" : "Create"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
